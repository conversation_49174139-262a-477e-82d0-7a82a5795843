<!DOCTYPE html>
<html>
<head>
  <title>Chat Room</title>
  <link rel="stylesheet" href="style.css" />
  <style>
    #room-info {
      background: #f0f0f0;
      padding: 10px;
      margin-bottom: 20px;
      border-radius: 5px;
    }
    #users-list {
      background: #f9f9f9;
      padding: 10px;
      margin-bottom: 20px;
      border-radius: 5px;
      max-height: 150px;
      overflow-y: auto;
    }
    .user-item {
      padding: 5px;
      margin: 2px 0;
      background: white;
      border-radius: 3px;
    }
    #leave-room {
      background: #dc3545;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 5px;
      cursor: pointer;
      margin-bottom: 20px;
    }
    #leave-room:hover {
      background: #c82333;
    }
  </style>
</head>
<body>
  <div id="chat-container">
    <div id="room-info">
      <h3 id="room-name">Room: </h3>
      <button id="leave-room">Leave Room</button>
    </div>
    
    <div id="users-list">
      <h4>Users in room:</h4>
      <div id="users-container"></div>
    </div>
    
    <div id="messages"></div>
    
    <div id="input-container">
      <input type="text" id="username" placeholder="Your name" />
      <input type="text" id="message-input" placeholder="Type a message..." />
      <button id="send-button">Send</button>
    </div>
  </div>
  
  <script src="room-client.js"></script>
</body>
</html>
