<!DOCTYPE html>
<html>
<head>
  <title>Chat Rooms</title>
  <link rel="stylesheet" href="style.css" />
  <style>
    .room-section {
      margin: 20px 0;
      padding: 20px;
      border: 1px solid #ddd;
      border-radius: 5px;
      background: #f9f9f9;
    }
    .room-list {
      margin: 20px 0;
    }
    .room-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px;
      margin: 5px 0;
      background: white;
      border-radius: 3px;
      border: 1px solid #eee;
    }
    .room-info {
      flex-grow: 1;
    }
    .room-name {
      font-weight: bold;
      margin-bottom: 5px;
    }
    .room-users {
      font-size: 0.9em;
      color: #666;
    }
    .join-button {
      background: #007bff;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 3px;
      cursor: pointer;
    }
    .join-button:hover {
      background: #0056b3;
    }
    .create-button {
      background: #28a745;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 5px;
      cursor: pointer;
    }
    .create-button:hover {
      background: #1e7e34;
    }
    #username-input, #room-name-input {
      width: 200px;
      padding: 8px;
      margin: 5px;
      border: 1px solid #ddd;
      border-radius: 3px;
    }
  </style>
</head>
<body>
  <div id="chat-container">
    <h1>Chat Rooms</h1>

    <div class="room-section">
      <h2>Create New Room</h2>
      <div>
        <input type="text" id="username-input" placeholder="Your name" required />
        <input type="text" id="room-name-input" placeholder="Room name" required />
        <button id="create-room" class="create-button">Create Room</button>
      </div>
    </div>

    <div class="room-section">
      <h2>Available Rooms</h2>
      <div id="rooms-list" class="room-list">
        <p>Loading rooms...</p>
      </div>
    </div>
  </div>

  <script src="index-client.js"></script>
</body>
</html>
