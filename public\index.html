<!DOCTYPE html>
<html>
<head>
  <title>WebSocket Chat</title>
  <link rel="stylesheet" href="style.css" />
</head>
<body>
  <div id="chat-container">
    <div id="messages"></div>
    <div id="input-container">
      <input type="text" id="username" placeholder="Your name" />
      <input type="text" id="message-input" placeholder="Type a message..." />
      <button id="send-button">Send</button>
    </div>
  </div>
  <script src="client.js"></script>
</body>
</html>
