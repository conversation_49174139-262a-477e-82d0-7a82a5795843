const express = require('express');
const http = require('http');
const WebSocket = require('ws');
const path = require('path');

const app = express();
const server = http.createServer(app);
const wss = new WebSocket.Server({ server });

app.use(express.static(path.join(__dirname, 'public')));

// Store rooms and users
const rooms = new Map(); // roomName -> { users: Set, messages: [] }

// Helper functions
const getRoomsList = () => {
  const roomsList = [];
  rooms.forEach((roomData, roomName) => {
    roomsList.push({
      name: roomName,
      userCount: roomData.users.size
    });
  });
  return roomsList;
};

const broadcastToRoom = (roomName, message, excludeWs = null) => {
  const room = rooms.get(roomName);
  if (!room) return;

  room.users.forEach((userWs) => {
    if (userWs !== excludeWs && userWs.readyState === WebSocket.OPEN) {
      userWs.send(JSON.stringify(message));
    }
  });
};

const getUsersInRoom = (roomName) => {
  const room = rooms.get(roomName);
  if (!room) return [];

  const users = [];
  room.users.forEach((userWs) => {
    if (userWs.username) {
      users.push(userWs.username);
    }
  });
  return users;
};

wss.on('connection', (ws) => {
  console.log('Client connected');

  ws.on('message', (message) => {
    try {
      const data = JSON.parse(message.toString());
      console.log('Received message:', data);

      switch (data.type) {
        case 'getRooms':
          ws.send(JSON.stringify({
            type: 'roomsList',
            rooms: getRoomsList()
          }));
          break;

        case 'createRoom':
          if (!rooms.has(data.room)) {
            rooms.set(data.room, {
              users: new Set(),
              messages: []
            });
            console.log(`Room created: ${data.room}`);
          }
          ws.send(JSON.stringify({
            type: 'roomCreated',
            room: data.room
          }));
          break;

        case 'join':
          const joinRoom = rooms.get(data.room);
          if (!joinRoom) {
            // Create room if it doesn't exist
            rooms.set(data.room, {
              users: new Set(),
              messages: []
            });
          }

          ws.room = data.room;
          ws.username = data.username;

          const room = rooms.get(data.room);
          room.users.add(ws);

          console.log(`${data.username} joined room: ${data.room}`);

          // Send user list to the joining user
          ws.send(JSON.stringify({
            type: 'userList',
            users: getUsersInRoom(data.room)
          }));

          // Notify other users in the room
          broadcastToRoom(data.room, {
            type: 'userJoined',
            username: data.username,
            users: getUsersInRoom(data.room)
          }, ws);
          break;

        case 'message':
          if (ws.room && ws.username) {
            const messageData = {
              type: 'message',
              username: data.username,
              text: data.text,
              timestamp: new Date().toISOString()
            };

            // Store message in room
            const msgRoom = rooms.get(ws.room);
            if (msgRoom) {
              msgRoom.messages.push(messageData);
            }

            // Broadcast to all users in the room
            broadcastToRoom(ws.room, messageData);
          }
          break;

        case 'leave':
          if (ws.room && ws.username) {
            const leaveRoom = rooms.get(ws.room);
            if (leaveRoom) {
              leaveRoom.users.delete(ws);

              // Notify other users
              broadcastToRoom(ws.room, {
                type: 'userLeft',
                username: ws.username,
                users: getUsersInRoom(ws.room)
              });

              // Remove empty rooms
              if (leaveRoom.users.size === 0) {
                rooms.delete(ws.room);
                console.log(`Room deleted: ${ws.room}`);
              }
            }

            console.log(`${ws.username} left room: ${ws.room}`);
            ws.room = null;
            ws.username = null;
          }
          break;
      }
    } catch (error) {
      console.error('Error processing message:', error);
    }
  });

  ws.on('close', () => {
    console.log('Client disconnected');

    // Clean up user from room
    if (ws.room && ws.username) {
      const room = rooms.get(ws.room);
      if (room) {
        room.users.delete(ws);

        // Notify other users
        broadcastToRoom(ws.room, {
          type: 'userLeft',
          username: ws.username,
          users: getUsersInRoom(ws.room)
        });

        // Remove empty rooms
        if (room.users.size === 0) {
          rooms.delete(ws.room);
          console.log(`Room deleted: ${ws.room}`);
        }
      }
    }
  });
});

const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
  console.log(`Server is listening on port ${PORT}`);
});
