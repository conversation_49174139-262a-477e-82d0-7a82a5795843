# Gemini 프로젝트: Node.js 웹소켓 채팅 애플리케이션

## 1. 프로젝트 목표

Node.js와 `ws` 라이브러리를 사용하여, 여러 클라이언트가 실시간으로 메시지를 주고받을 수 있는 간단한 웹 기반 채팅 애플리케이션을 구축합니다.

## 2. 기술 스택

- **백엔드**: Node.js, Express.js (정적 파일 서빙용), `ws` (웹소켓 처리용)
- **프론트엔드**: HTML, CSS, JavaScript

## 3. 파일 구조

```
/
├── GEMINI.md
├── package.json
├── server.js         # Express 및 웹소켓 서버 로직
└── public/
    ├── index.html    # 채팅 UI
    ├── style.css     # UI 스타일
    └── client.js     # 프론트엔드 웹소켓 로직
```

## 4. 개발 단계

1.  **프로젝트 초기화**
    - `npm init -y` 명령어로 `package.json` 생성
    - `npm install express ws` 명령어로 필요 라이브러리 설치

2.  **서버 (`server.js`) 구현**
    - Express 앱 생성 및 설정
    - `/` 경로 접근 시 `public` 폴더의 정적 파일들(html, css, js)을 제공하도록 설정
    - `ws` 라이브러리를 사용하여 웹소켓 서버 생성
    - 클라이언트 연결 시 이벤트 처리
        - 새로운 클라이언트 연결 시 환영 메시지 또는 접속 알림
        - 클라이언트로부터 메시지 수신 시, 연결된 모든 클라이언트에게 메시지 브로드캐스팅
        - 클라이언트 연결 종료 시 처리

3.  **클라이언트 (`public/`) 구현**
    - **`index.html`**:
        - 메시지가 표시될 영역 (`<div>`)
        - 사용자 이름 입력 필드 (`<input type="text">`)
        - 메시지 입력 필드 (`<input type="text">`)
        - 메시지 전송 버튼 (`<button>`)
    - **`style.css`**:
        - 기본적인 채팅 UI 스타일링 (메시지 창, 입력 필드 등)
    - **`client.js`**:
        - 페이지 로드 시 서버의 웹소켓과 연결
        - 전송 버튼 클릭 또는 Enter 키 입력 시, 입력된 메시지를 서버로 전송
        - 서버로부터 메시지 수신 시, 메시지 영역에 새로운 메시지 추가
        - 연결, 종료 등 이벤트에 대한 기본적인 처리

## 5. 실행 방법

1.  터미널에서 `npm install` 실행
2.  `node server.js` 명령어로 서버 시작
3.  웹 브라우저를 열고 `http://localhost:3000` 주소로 접속 (여러 브라우저 탭에서 열어 테스트)
