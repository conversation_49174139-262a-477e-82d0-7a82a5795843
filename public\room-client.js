const messages = document.getElementById('messages');
const usernameInput = document.getElementById('username');
const messageInput = document.getElementById('message-input');
const sendButton = document.getElementById('send-button');
const leaveRoomButton = document.getElementById('leave-room');
const roomNameElement = document.getElementById('room-name');
const usersContainer = document.getElementById('users-container');

// Get room name and username from URL parameters
const urlParams = new URLSearchParams(window.location.search);
const roomName = urlParams.get('room');
const username = urlParams.get('username');

if (!roomName || !username) {
  alert('Room name or username not provided!');
  window.location.href = '/';
}

roomNameElement.textContent = `Room: ${roomName}`;
usernameInput.value = username;
usernameInput.disabled = true; // Disable username change in room

const socket = new WebSocket(`ws://${window.location.host}`);

socket.onopen = () => {
  console.log('WebSocket connection established.');
  // Join the room
  const joinMessage = {
    type: 'join',
    room: roomName,
    username: username
  };
  socket.send(JSON.stringify(joinMessage));
};

socket.onmessage = (event) => {
  const data = JSON.parse(event.data);
  
  if (data.type === 'message') {
    displayMessage(data);
  } else if (data.type === 'userList') {
    updateUsersList(data.users);
  } else if (data.type === 'userJoined') {
    displaySystemMessage(`${data.username} joined the room`);
    updateUsersList(data.users);
  } else if (data.type === 'userLeft') {
    displaySystemMessage(`${data.username} left the room`);
    updateUsersList(data.users);
  }
};

socket.onclose = () => {
  console.log('WebSocket connection closed.');
};

const displayMessage = (messageData) => {
  const messageElement = document.createElement('div');
  messageElement.classList.add('message');

  const usernameElement = document.createElement('span');
  usernameElement.classList.add('username');
  usernameElement.textContent = `${messageData.username}: `;

  const textElement = document.createElement('span');
  textElement.textContent = messageData.text;

  messageElement.appendChild(usernameElement);
  messageElement.appendChild(textElement);
  messages.appendChild(messageElement);
  messages.scrollTop = messages.scrollHeight;
};

const displaySystemMessage = (text) => {
  const messageElement = document.createElement('div');
  messageElement.classList.add('message', 'system-message');
  messageElement.textContent = text;
  messageElement.style.fontStyle = 'italic';
  messageElement.style.color = '#666';
  messages.appendChild(messageElement);
  messages.scrollTop = messages.scrollHeight;
};

const updateUsersList = (users) => {
  usersContainer.innerHTML = '';
  users.forEach(user => {
    const userElement = document.createElement('div');
    userElement.classList.add('user-item');
    userElement.textContent = user;
    usersContainer.appendChild(userElement);
  });
};

const sendMessage = () => {
  const text = messageInput.value.trim();

  if (text) {
    const message = {
      type: 'message',
      room: roomName,
      username: username,
      text: text,
    };
    socket.send(JSON.stringify(message));
    messageInput.value = '';
  }
};

const leaveRoom = () => {
  const leaveMessage = {
    type: 'leave',
    room: roomName,
    username: username
  };
  socket.send(JSON.stringify(leaveMessage));
  window.location.href = '/';
};

sendButton.addEventListener('click', sendMessage);
leaveRoomButton.addEventListener('click', leaveRoom);

messageInput.addEventListener('keydown', (event) => {
  if (event.key === 'Enter') {
    sendMessage();
  }
});

// Handle page unload (browser close/refresh)
window.addEventListener('beforeunload', () => {
  const leaveMessage = {
    type: 'leave',
    room: roomName,
    username: username
  };
  socket.send(JSON.stringify(leaveMessage));
});
