const usernameInput = document.getElementById('username-input');
const roomNameInput = document.getElementById('room-name-input');
const createRoomButton = document.getElementById('create-room');
const roomsList = document.getElementById('rooms-list');

const socket = new WebSocket(`ws://${window.location.host}`);

socket.onopen = () => {
  console.log('WebSocket connection established.');
  // Request room list
  socket.send(JSON.stringify({ type: 'getRooms' }));
};

socket.onmessage = (event) => {
  const data = JSON.parse(event.data);
  
  if (data.type === 'roomsList') {
    updateRoomsList(data.rooms);
  } else if (data.type === 'roomCreated') {
    // Refresh room list after creating a room
    socket.send(JSON.stringify({ type: 'getRooms' }));
  }
};

socket.onclose = () => {
  console.log('WebSocket connection closed.');
};

const updateRoomsList = (rooms) => {
  if (rooms.length === 0) {
    roomsList.innerHTML = '<p>No rooms available. Create one!</p>';
    return;
  }
  
  roomsList.innerHTML = '';
  rooms.forEach(room => {
    const roomElement = document.createElement('div');
    roomElement.classList.add('room-item');
    
    const roomInfo = document.createElement('div');
    roomInfo.classList.add('room-info');
    
    const roomName = document.createElement('div');
    roomName.classList.add('room-name');
    roomName.textContent = room.name;
    
    const roomUsers = document.createElement('div');
    roomUsers.classList.add('room-users');
    roomUsers.textContent = `${room.userCount} user(s) online`;
    
    roomInfo.appendChild(roomName);
    roomInfo.appendChild(roomUsers);
    
    const joinButton = document.createElement('button');
    joinButton.classList.add('join-button');
    joinButton.textContent = 'Join';
    joinButton.onclick = () => joinRoom(room.name);
    
    roomElement.appendChild(roomInfo);
    roomElement.appendChild(joinButton);
    roomsList.appendChild(roomElement);
  });
};

const createRoom = () => {
  const username = usernameInput.value.trim();
  const roomName = roomNameInput.value.trim();
  
  if (!username) {
    alert('Please enter your name');
    return;
  }
  
  if (!roomName) {
    alert('Please enter a room name');
    return;
  }
  
  // Create room and join immediately
  const createMessage = {
    type: 'createRoom',
    room: roomName,
    username: username
  };
  
  socket.send(JSON.stringify(createMessage));
  
  // Navigate to room
  window.location.href = `/room.html?room=${encodeURIComponent(roomName)}&username=${encodeURIComponent(username)}`;
};

const joinRoom = (roomName) => {
  const username = usernameInput.value.trim();
  
  if (!username) {
    alert('Please enter your name to join a room');
    return;
  }
  
  // Navigate to room
  window.location.href = `/room.html?room=${encodeURIComponent(roomName)}&username=${encodeURIComponent(username)}`;
};

createRoomButton.addEventListener('click', createRoom);

// Allow creating room with Enter key
roomNameInput.addEventListener('keydown', (event) => {
  if (event.key === 'Enter') {
    createRoom();
  }
});

usernameInput.addEventListener('keydown', (event) => {
  if (event.key === 'Enter') {
    createRoom();
  }
});

// Refresh room list every 5 seconds
setInterval(() => {
  if (socket.readyState === WebSocket.OPEN) {
    socket.send(JSON.stringify({ type: 'getRooms' }));
  }
}, 5000);
