// This file is no longer used - functionality moved to index-client.js and room-client.js
// Keeping for reference/backup purposes

/*
const messages = document.getElementById('messages');
const usernameInput = document.getElementById('username');
const messageInput = document.getElementById('message-input');
const sendButton = document.getElementById('send-button');

const socket = new WebSocket(`ws://${window.location.host}`);

socket.onopen = () => {
  console.log('WebSocket connection established.');
};

socket.onmessage = (event) => {
  const messageData = JSON.parse(event.data);
  const messageElement = document.createElement('div');
  messageElement.classList.add('message');

  const usernameElement = document.createElement('span');
  usernameElement.classList.add('username');
  usernameElement.textContent = `${messageData.username}: `;

  const textElement = document.createElement('span');
  textElement.textContent = messageData.text;

  messageElement.appendChild(usernameElement);
  messageElement.appendChild(textElement);
  messages.appendChild(messageElement);
  messages.scrollTop = messages.scrollHeight;
};

socket.onclose = () => {
  console.log('WebSocket connection closed.');
};

const sendMessage = () => {
  const username = usernameInput.value.trim();
  const text = messageInput.value.trim();

  if (username && text) {
    const message = {
      username: username,
      text: text,
    };
    socket.send(JSON.stringify(message));
    messageInput.value = '';
  }
};

sendButton.addEventListener('click', sendMessage);

messageInput.addEventListener('keydown', (event) => {
  if (event.key === 'Enter') {
    sendMessage();
  }
});
*/
